#!/usr/bin/env python3
"""
测试防卡死修复
模拟API配额错误场景，验证系统不会卡死
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from complete_report_generator import CompleteReportGenerator, AsyncConfig

async def simulate_quota_errors():
    """模拟API配额错误场景"""
    print("🧪 模拟API配额错误场景...")
    
    try:
        generator = CompleteReportGenerator(use_async=True, max_tokens=250000)
        api_manager = generator.api_manager
        
        # 人为制造配额错误
        print("🔧 人为制造配额错误状态...")
        current_time = time.time()
        
        for i, config in enumerate(api_manager.api_configs):
            if i < 8:  # 前8个API设置为配额错误
                config["consecutive_quota_errors"] = 3
                config["backoff_until"] = current_time + 60  # 1分钟退避
                config["is_available"] = False
                print(f"   ❌ 设置 {config['name']} 为配额错误状态")
            elif i < 9:  # 第9个API设置为其他错误
                config["error_count"] = 10
                config["is_available"] = False
                config["backoff_until"] = current_time + 120
                print(f"   ❌ 设置 {config['name']} 为错误过多状态")
        
        # 测试在这种情况下是否能获取到API配置
        print("\n🔍 测试获取可用API配置...")
        api_config = api_manager._get_available_api_config()
        
        if api_config:
            print(f"✅ 成功获取API配置: {api_config['api_name']}")
            return True
        else:
            print("❌ 无法获取API配置")
            return False
            
    except Exception as e:
        print(f"❌ 模拟测试失败: {str(e)}")
        return False

async def test_api_call_with_errors():
    """测试在错误状态下的API调用"""
    print("\n🧪 测试在错误状态下的API调用...")
    
    try:
        generator = CompleteReportGenerator(use_async=True, max_tokens=250000)
        
        # 模拟一个简单的API调用
        test_prompt = "请简单说明人工智能的定义，不超过50字。"
        
        print("🔄 尝试API调用...")
        start_time = time.time()
        
        # 设置较短的超时时间来测试
        try:
            response, api_index = await asyncio.wait_for(
                generator.api_manager.generate_content_with_model_async(
                    test_prompt, "gemini-2.5-flash"
                ),
                timeout=60  # 1分钟超时
            )
            
            end_time = time.time()
            elapsed = end_time - start_time
            
            print(f"✅ API调用成功，耗时: {elapsed:.2f}秒")
            print(f"   使用API: {api_index}")
            
            # 检查响应内容
            if hasattr(response, 'text'):
                content = response.text[:100]
            else:
                content = str(response)[:100]
            
            print(f"   响应内容: {content}...")
            return True
            
        except asyncio.TimeoutError:
            print("❌ API调用超时")
            return False
            
    except Exception as e:
        print(f"❌ API调用测试失败: {str(e)}")
        return False

async def test_force_reset_mechanism():
    """测试强制重置机制"""
    print("\n🧪 测试强制重置机制...")
    
    try:
        generator = CompleteReportGenerator(use_async=True, max_tokens=250000)
        api_manager = generator.api_manager
        
        # 设置所有API为不可用状态
        print("🔧 设置所有API为不可用状态...")
        current_time = time.time()
        
        for config in api_manager.api_configs:
            config["consecutive_quota_errors"] = 5
            config["backoff_until"] = current_time + 300  # 5分钟退避
            config["is_available"] = False
            config["error_count"] = 10
        
        # 记录状态
        print("📊 重置前API状态:")
        api_manager._log_api_status_summary()
        
        # 执行强制重置
        print("\n🔄 执行强制重置...")
        api_manager._force_reset_all_apis()
        
        # 检查重置后状态
        print("\n📊 重置后API状态:")
        has_available = api_manager._log_api_status_summary()
        
        if has_available:
            print("✅ 强制重置成功，有可用API")
            return True
        else:
            print("❌ 强制重置失败，仍无可用API")
            return False
            
    except Exception as e:
        print(f"❌ 强制重置测试失败: {str(e)}")
        return False

async def test_batch_execution_resilience():
    """测试批处理执行的抗错误能力"""
    print("\n🧪 测试批处理执行的抗错误能力...")
    
    try:
        generator = CompleteReportGenerator(use_async=True, max_tokens=250000)
        
        # 创建一些会失败的任务
        async def error_prone_task(task_id):
            if task_id % 2 == 0:
                # 模拟API错误
                raise Exception("模拟API配额错误")
            else:
                await asyncio.sleep(0.1)
                return f"任务 {task_id} 完成"
        
        tasks = [error_prone_task(i) for i in range(10)]
        
        print("🔄 执行容错批处理...")
        start_time = time.time()
        
        # 执行批处理，应该能处理错误而不卡死
        await generator._execute_tasks_in_batches(tasks, 5, "容错测试")
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        print(f"✅ 批处理完成，耗时: {elapsed:.2f}秒")
        
        # 检查是否在合理时间内完成
        if elapsed < 30:  # 30秒内完成
            return True
        else:
            print(f"⚠️ 批处理耗时过长: {elapsed:.2f}秒")
            return False
            
    except Exception as e:
        print(f"❌ 批处理容错测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始防卡死修复测试")
    print("="*60)
    
    # 测试结果
    results = []
    
    # 1. 模拟配额错误
    results.append(await simulate_quota_errors())
    
    # 2. 测试API调用容错
    results.append(await test_api_call_with_errors())
    
    # 3. 测试强制重置机制
    results.append(await test_force_reset_mechanism())
    
    # 4. 测试批处理容错
    results.append(await test_batch_execution_resilience())
    
    # 汇总结果
    print("\n" + "="*60)
    print("🎯 防卡死测试结果:")
    test_names = [
        "配额错误模拟",
        "API调用容错",
        "强制重置机制",
        "批处理容错"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有防卡死测试都通过！")
        print("\n📋 修复要点:")
        print("   1. ✅ 放宽了配额检查限制")
        print("   2. ✅ 缩短了退避时间")
        print("   3. ✅ 添加了强制重置机制")
        print("   4. ✅ 改进了错误处理逻辑")
        print("   5. ✅ 增加了API状态监控")
        print("   6. ✅ 优化了重试策略")
        return True
    else:
        print("⚠️ 部分测试失败，可能仍存在卡死风险")
        return False

if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
